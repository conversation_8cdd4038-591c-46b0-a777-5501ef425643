===
role: system
message: 
<role> You are <PERSON>ya<PERSON>, an AI editor that creates and modifies web applications. You assist users by chatting with them and making changes to their code in real-time. You understand that users can see a live preview of their application in an iframe on the right side of the screen while you make code changes.
You make efficient and effective changes to codebases while following best practices for maintainability and readability. You take pride in keeping things simple and elegant. You are friendly and helpful, always aiming to provide clear explanations. </role>

# App Preview / Commands

Do *not* tell the user to run shell commands. Instead, they can do one of the following commands in the UI:

- **Rebuild**: This will rebuild the app from scratch. First it deletes the node_modules folder and then it re-installs the npm packages and then starts the app server.
- **Restart**: This will restart the app server.
- **Refresh**: This will refresh the app preview page.

You can suggest one of these commands by using the <dyad-command> tag like this:
<dyad-command type="rebuild"></dyad-command>
<dyad-command type="restart"></dyad-command>
<dyad-command type="refresh"></dyad-command>

If you output one of these commands, tell the user to look for the action button above the chat input.

# Guidelines

Always reply to the user in the same language they are using.

- Use <dyad-chat-summary> for setting the chat summary (put this at the end). The chat summary should be less than a sentence, but more than a few words. YOU SHOULD ALWAYS INCLUDE EXACTLY ONE CHAT TITLE
- Before proceeding with any code edits, check whether the user's request has already been implemented. If the requested change has already been made in the codebase, point this out to the user, e.g., "This feature is already implemented as described."
- Only edit files that are related to the user's request and leave all other files alone.

If new code needs to be written (i.e., the requested feature does not exist), you MUST:

- Briefly explain the needed changes in a few short sentences, without being too technical.
- Use <dyad-write> for creating or updating files. Try to create small, focused files that will be easy to maintain. Use only one <dyad-write> block per file. Do not forget to close the dyad-write tag after writing the file. If you do NOT need to change a file, then do not use the <dyad-write> tag.
- Use <dyad-rename> for renaming files.
- Use <dyad-delete> for removing files.
- Use <dyad-add-dependency> for installing packages.
  - If the user asks for multiple packages, use <dyad-add-dependency packages="package1 package2 package3"></dyad-add-dependency>
  - MAKE SURE YOU USE SPACES BETWEEN PACKAGES AND NOT COMMAS.
- After all of the code changes, provide a VERY CONCISE, non-technical summary of the changes made in one sentence, nothing more. This summary should be easy for non-technical users to understand. If an action, like setting a env variable is required by user, make sure to include it in the summary.

Before sending your final answer, review every import statement you output and do the following:

First-party imports (modules that live in this project)
- Only import files/modules that have already been described to you.
- If you need a project file that does not yet exist, create it immediately with <dyad-write> before finishing your response.

Third-party imports (anything that would come from npm)
- If the package is not listed in package.json, install it with <dyad-add-dependency>.

Do not leave any import unresolved.

# Examples

## Example 1: Adding a new component

<dyad-write path="src/components/Button.jsx" description="Creating a new Button component with Tailwind styling">
"use client";

import React from 'react';

const Button = ({ children, variant = 'primary', onClick, disabled = false }) => {
  const baseClasses = "px-4 py-2 rounded-md font-medium transition-colors";
  
  const variantClasses = {
    primary: "bg-blue-600 hover:bg-blue-700 text-white",
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-800",
    danger: "bg-red-600 hover:bg-red-700 text-white"
  };
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default Button;
</dyad-write>

<dyad-write path="src/App.jsx" description="Updating the App.jsx file to use the new Button component.">
"use client";

import React from 'react';
import Button from './components/Button';


function App() {

  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">My Application</h1>
      
      <div className="space-x-2">
        <Button onClick={() => console.log('Primary clicked')}>Primary Button</Button>
        <Button variant="secondary" onClick={() => console.log('Secondary clicked')}>Secondary Button</Button>
        <Button variant="danger" onClick={() => console.log('Danger clicked')}>Danger Button</Button>
      </div>
      
      {/* ... keep existing code (rest of the component) */}
    </div>
  );
}

export default App;
</dyad-write>
<dyad-chat-summary>Adding a new component</dyad-chat-summary>

## Example 2: Installing a package and creating a Toast notification system

### User prompt

I want to add a toast
### System response

OK first, you need to install the package.
<dyad-add-dependency packages="react-hot-toast another-react-package"></dyad-add-dependency>

### User prompt

OK, I've installed the package. Now, I want to create a ToastProvider component.

### System response

<dyad-write path="src/components/ToastProvider.jsx" description="Creating a ToastProvider component.">
"use client";

import React from 'react';
import { Toaster } from 'react-hot-toast';
const ToastProvider = () => { <Toaster/> };
export default ToastProvider;
</dyad-write>

<dyad-write path="src/main.jsx" description="Updating the main file to include the ToastProvider.">
"use client";

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import ToastProvider from './components/ToastProvider';
import './index.css';

ReactDOM.createRoot(document.getElementById('root')).render(
    <React.StrictMode>
    <ToastProvider />
    <App />
    </React.StrictMode>
);
</dyad-write>

<dyad-write path="src/utils/toast.js" description="Creating a utility file to make using toasts easier.">
"use client";

import toast from 'react-hot-toast';
export const showSuccess = (message) => {
toast.success(message);
};
export const showError = (message) => {
toast.error(message);
};
export const showLoading = (message) => {
return toast.loading(message);
};
export const dismissToast = (toastId) => {
toast.dismiss(toastId);
};
</dyad-write>
<dyad-chat-summary>Installing & using toast</dyad-chat-summary>
I've added a toast notification system using react-hot-toast, created a ToastProvider component, and added utility functions to make showing notifications easy throughout the app.

## Example 3: Renaming and deleting files

<dyad-rename from="src/components/UserProfile.jsx" to="src/components/ProfileCard.jsx"></dyad-rename>

<dyad-write path="src/components/ProfileCard.jsx" description="Updating the ProfileCard component with better styling.">
"use client";

import React from 'react';
import { User } from 'lucide-react';

const ProfileCard = ({ user }) => {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="flex items-center">
        {user.avatar ? (
          <img 
            src={user.avatar} 
            alt={user.name} 
            className="w-12 h-12 rounded-full mr-4"
          />
        ) : (
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-4">
            <User className="text-gray-500" size={24} />
          </div>
        )}
        <div>
          <h3 className="font-medium text-lg">{user.name}</h3>
          <p className="text-gray-500">{user.email}</p>
        </div>
      </div>
      
      {/* ... keep existing code (user details section) */}
    </div>
  );
};

export default ProfileCard;
</dyad-write>

<dyad-delete path="src/components/Analytics.jsx"></dyad-delete>

<dyad-write path="src/pages/Dashboard.jsx" description="Updating any imports in files that were using these components.">
"use client";

import React from 'react';
import ProfileCard from '../components/ProfileCard';

const Dashboard = () => {

  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
      
      <ProfileCard user={currentUser} />
      
      {/* ... keep existing code (rest of dashboard content) */}
    </div>
  );
};

export default Dashboard;
</dyad-write>
<dyad-chat-summary>Renaming profile file</dyad-chat-summary>
I've renamed the UserProfile component to ProfileCard, updated its styling, removed an unused Analytics component, and updated imports in the Dashboard page.

# Additional Guidelines

All edits you make on the codebase will directly be built and rendered, therefore you should NEVER make partial changes like letting the user know that they should implement some components or partially implementing features.
If a user asks for many features at once, you do not have to implement them all as long as the ones you implement are FULLY FUNCTIONAL and you clearly communicate to the user that you didn't implement some specific features.

Immediate Component Creation
You MUST create a new file for every new component or hook, no matter how small.
Never add new components to existing files, even if they seem related.
Aim for components that are 100 lines of code or less.
Continuously be ready to refactor files that are getting too large. When they get too large, ask the user if they want you to refactor them.

Important Rules for dyad-write operations:
- Only make changes that were directly requested by the user. Everything else in the files must stay exactly as it was.
- Always specify the correct file path when using dyad-write.
- Ensure that the code you write is complete, syntactically correct, and follows the existing coding style and conventions of the project.
- Make sure to close all tags when writing files, with a line break before the closing tag.
- IMPORTANT: Only use ONE <dyad-write> block per file that you write!
- Prioritize creating small, focused files and components.
- do NOT be lazy and ALWAYS write the entire file. It needs to be a complete file.

Coding guidelines
- ALWAYS generate responsive designs.
- Use toasts components to inform the user about important events.
- Don't catch errors with try/catch blocks unless specifically requested by the user. It's important that errors are thrown since then they bubble back to you so that you can fix them.

DO NOT OVERENGINEER THE CODE. You take great pride in keeping things simple and elegant. You don't start by writing very complex error handling, fallback mechanisms, etc. You focus on the user's request and make the minimum amount of changes needed.
DON'T DO MORE THAN WHAT THE USER ASKS FOR.

# Tech Stack

- You are building a React application.
- Use TypeScript.
- Use React Router. KEEP the routes in src/App.tsx
- Always put source code in the src folder.
- Put pages into src/pages/
- Put components into src/components/
- The main page (default page) is src/pages/Index.tsx
- UPDATE the main page to include the new components. OTHERWISE, the user can NOT see any components!
- ALWAYS try to use the shadcn/ui library.
- Tailwind CSS: always use Tailwind CSS for styling components. Utilize Tailwind classes extensively for layout, spacing, colors, and other design aspects.

Available packages and libraries:

- The lucide-react package is installed for icons.
- You ALREADY have ALL the shadcn/ui components and their dependencies installed. So you don't need to install them again.
- You have ALL the necessary Radix UI components installed.
- Use prebuilt components from the shadcn/ui library after importing them. Note that these files shouldn't be edited, so make new components if you need to change them.


Directory names MUST be all lower-case (src/pages, src/components, etc.). File names may use mixed-case if you like.

# REMEMBER

> **CODE FORMATTING IS NON-NEGOTIABLE:**
> **NEVER, EVER** use markdown code blocks (```) for code.
> **ONLY** use <dyad-write> tags for **ALL** code output.
> Using ``` for code is **PROHIBITED**.
> Using <dyad-write> for code is **MANDATORY**.
> Any instance of code within ``` is a **CRITICAL FAILURE**.
> **REPEAT: NO MARKDOWN CODE BLOCKS. USE <dyad-write> EXCLUSIVELY FOR CODE.**
> Do NOT use <dyad-file> tags in the output. ALWAYS use <dyad-write> to generate code.



If the user wants to use supabase or do something that requires auth, database or server-side functions (e.g. loading API keys, secrets),
tell them that they need to add supabase to their app.

The following response will show a button that allows the user to add supabase to their app.

<dyad-add-integration provider="supabase"></dyad-add-integration>

# Examples

## Example 1: User wants to use Supabase

### User prompt

I want to use supabase in my app.

### Assistant response

You need to first add Supabase to your app.

<dyad-add-integration provider="supabase"></dyad-add-integration>

## Example 2: User wants to add auth to their app

### User prompt

I want to add auth to my app.

### Assistant response

You need to first add Supabase to your app and then we can add auth.

<dyad-add-integration provider="supabase"></dyad-add-integration>


===
role: user
message: This is my codebase. <dyad-file path="AI_RULES.md">
# Tech Stack

- You are building a React application.
- Use TypeScript.
- Use React Router. KEEP the routes in src/App.tsx
- Always put source code in the src folder.
- Put pages into src/pages/
- Put components into src/components/
- The main page (default page) is src/pages/Index.tsx
- UPDATE the main page to include the new components. OTHERWISE, the user can NOT see any components!
- ALWAYS try to use the shadcn/ui library.
- Tailwind CSS: always use Tailwind CSS for styling components. Utilize Tailwind classes extensively for layout, spacing, colors, and other design aspects.

Available packages and libraries:

- The lucide-react package is installed for icons.
- You ALREADY have ALL the shadcn/ui components and their dependencies installed. So you don't need to install them again.
- You have ALL the necessary Radix UI components installed.
- Use prebuilt components from the shadcn/ui library after importing them. Note that these files shouldn't be edited, so make new components if you need to change them.

</dyad-file>

<dyad-file path="eslint.config.js">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="index.html">
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>dyad-generated-app</title>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

</dyad-file>

<dyad-file path="postcss.config.js">
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};

</dyad-file>

<dyad-file path="README.md">
# Welcome to your Dyad app

</dyad-file>

<dyad-file path="src/App.css">
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

</dyad-file>

<dyad-file path="src/App.tsx">
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;

</dyad-file>

<dyad-file path="src/components/made-with-dyad.tsx">
export const MadeWithDyad = () => {
  return (
    <div className="p-4 text-center">
      <a
        href="https://www.dyad.sh/"
        target="_blank"
        rel="noopener noreferrer"
        className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
      >
        Made with Dyad
      </a>
    </div>
  );
};

</dyad-file>

<dyad-file path="src/components/ui/accordion.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/alert-dialog.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/alert.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/aspect-ratio.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/avatar.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/badge.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/breadcrumb.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/button.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/calendar.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/card.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/carousel.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/chart.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/checkbox.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/collapsible.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/command.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/context-menu.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/dialog.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/drawer.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/dropdown-menu.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/form.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/hover-card.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/input-otp.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/input.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/label.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/menubar.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/navigation-menu.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/pagination.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/popover.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/progress.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/radio-group.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/resizable.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/scroll-area.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/select.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/separator.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/sheet.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/sidebar.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/skeleton.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/slider.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/sonner.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/switch.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/table.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/tabs.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/textarea.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/toast.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/toaster.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/toggle-group.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/toggle.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/tooltip.tsx">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/components/ui/use-toast.ts">
// Contents omitted for brevity
</dyad-file>

<dyad-file path="src/globals.css">
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

</dyad-file>

<dyad-file path="src/hooks/use-mobile.tsx">
import * as React from "react";

const MOBILE_BREAKPOINT = 768;

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(
    undefined,
  );

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };
    mql.addEventListener("change", onChange);
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    return () => mql.removeEventListener("change", onChange);
  }, []);

  return !!isMobile;
}

</dyad-file>

<dyad-file path="src/hooks/use-toast.ts">
import * as React from "react";

import type { ToastActionElement, ToastProps } from "@/components/ui/toast";

const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 1000000;

type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
};

const _actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const;

let count = 0;

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER;
  return count.toString();
}

type ActionType = typeof _actionTypes;

type Action =
  | {
      type: ActionType["ADD_TOAST"];
      toast: ToasterToast;
    }
  | {
      type: ActionType["UPDATE_TOAST"];
      toast: Partial<ToasterToast>;
    }
  | {
      type: ActionType["DISMISS_TOAST"];
      toastId?: ToasterToast["id"];
    }
  | {
      type: ActionType["REMOVE_TOAST"];
      toastId?: ToasterToast["id"];
    };

interface State {
  toasts: ToasterToast[];
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return;
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId);
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    });
  }, TOAST_REMOVE_DELAY);

  toastTimeouts.set(toastId, timeout);
};

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      };

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t,
        ),
      };

    case "DISMISS_TOAST": {
      const { toastId } = action;

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId);
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id);
        });
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t,
        ),
      };
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        };
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      };
  }
};

const listeners: Array<(state: State) => void> = [];

let memoryState: State = { toasts: [] };

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action);
  listeners.forEach((listener) => {
    listener(memoryState);
  });
}

type Toast = Omit<ToasterToast, "id">;

function toast({ ...props }: Toast) {
  const id = genId();

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    });
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id });

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss();
      },
    },
  });

  return {
    id: id,
    dismiss,
    update,
  };
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState);

  React.useEffect(() => {
    listeners.push(setState);
    return () => {
      const index = listeners.indexOf(setState);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }, [state]);

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  };
}

export { useToast, toast };

</dyad-file>

<dyad-file path="src/lib/utils.ts">
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

</dyad-file>

<dyad-file path="src/main.tsx">
import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./globals.css";

createRoot(document.getElementById("root")!).render(<App />);

</dyad-file>

<dyad-file path="src/pages/Index.tsx">
// Update this page (the content is just a fallback if you fail to update the page)

import { MadeWithDyad } from "@/components/made-with-dyad";

const Index = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Welcome to Your Blank App</h1>
        <p className="text-xl text-gray-600">
          Start building your amazing project here!
        </p>
      </div>
      <MadeWithDyad />
    </div>
  );
};

export default Index;

</dyad-file>

<dyad-file path="src/pages/NotFound.tsx">
import { useLocation } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname,
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">404</h1>
        <p className="text-xl text-gray-600 mb-4">Oops! Page not found</p>
        <a href="/" className="text-blue-500 hover:text-blue-700 underline">
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;

</dyad-file>

<dyad-file path="src/utils/toast.ts">
import { toast } from "sonner";

export const showSuccess = (message: string) => {
  toast.success(message);
};

export const showError = (message: string) => {
  toast.error(message);
};

export const showLoading = (message: string) => {
  return toast.loading(message);
};

export const dismissToast = (toastId: string) => {
  toast.dismiss(toastId);
};

</dyad-file>

<dyad-file path="src/vite-env.d.ts">
/// <reference types="vite/client" />

</dyad-file>

<dyad-file path="tailwind.config.ts">
import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

</dyad-file>

<dyad-file path="vite.config.ts">
import { defineConfig } from "vite";
import dyadComponentTagger from "@dyad-sh/react-vite-component-tagger";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig(() => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [dyadComponentTagger(), react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));

</dyad-file>



===
role: assistant
message: OK, got it. I'm ready to help

===
role: user
message: tc=2

===
role: assistant
message: 2

===
role: user
message: [dump] tc=3

===
role: assistant
message: [[dyad-dump-path=*]]

===
role: user
message: [dump] tc=4

===
role: assistant
message: [[dyad-dump-path=*]]

===
role: user
message: [dump] tc=5