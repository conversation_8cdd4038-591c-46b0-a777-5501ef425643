{"name": "fake-llm-server", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "fake-llm-server", "version": "1.0.0", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "stream": "0.0.2"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.21", "@types/node": "^20.17.46", "git-http-mock-server": "^2.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.5", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cors": {"version": "2.8.18", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/express": {"version": "4.17.21", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.19.6", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "20.17.46", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.19.2"}}, "node_modules/@types/qs": {"version": "6.9.18", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.1", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/apache-crypt": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/apache-crypt/-/apache-crypt-1.2.6.tgz", "integrity": "sha512-072WetlM4blL8PREJVeY+WHiUh1R5VNt2HfceGS8aKqttPHcmqE5pkKuXPz/ULmJOFkc8Hw3kfKl6vy7Qka6DA==", "dev": true, "license": "MIT", "dependencies": {"unix-crypt-td-js": "^1.1.4"}, "engines": {"node": ">=8"}}, "node_modules/apache-md5": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/apache-md5/-/apache-md5-1.1.8.tgz", "integrity": "sha512-FCAJojipPn0bXjuEpjOOOMN8FZDkxfWWp4JGN9mifU2IhxvKyXZYqpzPHdnTSUpmPDy+tsslB6Z1g+Vg6nVbYA==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/arg": {"version": "4.1.3", "dev": true, "license": "MIT"}, "node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/array-union": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "integrity": "sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng==", "dev": true, "license": "MIT", "dependencies": {"array-uniq": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true, "license": "MIT"}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/basic-auth/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true, "license": "MIT"}, "node_modules/bcryptjs": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz", "integrity": "sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ==", "dev": true, "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.3", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true, "license": "MIT"}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/create-require": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/crypto-random-string": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-1.0.0.tgz", "integrity": "sha512-GsVpkFPlycH7/fRR7Dhcmnoii54gV1nz7y4CWyeFS14N+JVBBhY+r8amRHE4BwSYal7BPTDp8isvAlCxyFt3Hg==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/daemonize-process": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/daemonize-process/-/daemonize-process-1.0.9.tgz", "integrity": "sha512-Yo<PERSON>+AmcgHIBDVeyfVWSCV90FNk799zX8Uvn7RJTDCD8Y0EMNbSfIKLG961VgchJme2GHmqpXUuV8Rxe2j2L+bw==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4"}}, "node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/diff": {"version": "4.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dir-glob": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-2.2.2.tgz", "integrity": "sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw==", "dev": true, "license": "MIT", "dependencies": {"path-type": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/emitter-component": {"version": "1.1.2", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express": {"version": "4.21.2", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/finalhandler": {"version": "1.3.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/fixturez": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fixturez/-/fixturez-1.1.0.tgz", "integrity": "sha512-c4q9eZsAmCzj9gkrEO/YwIRlrHWt/TXQiX9jR9WeLFOqeeV6EyzdiiV28CpSzF6Ip+gyYrSv5UeOHqyzfcNTVA==", "dev": true, "license": "MIT", "dependencies": {"fs-extra": "^5.0.0", "globby": "^7.1.1", "signal-exit": "^3.0.2", "tempy": "^0.2.1"}}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-5.0.0.tgz", "integrity": "sha512-66Pm4RYbjzdyeuqudYqhFiNBbCIuI9kgRqLPSHIlXHidW8NIQtVdkM1yeZ4lXwuhbTETv3EUGMNHAAw6hiundQ==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/git-http-mock-server": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/git-http-mock-server/-/git-http-mock-server-2.0.0.tgz", "integrity": "sha512-LOCls7jjuzwfKmUbcFsqj2yIEqExBzv0rA1tL7j1ULhRLAax4U1Bd/rbU9ebtri1ldzgcPD1VAyuhS1pvDC2pA==", "dev": true, "license": "MIT", "dependencies": {"basic-auth": "^2.0.0", "buffer-equal-constant-time": "^1.0.1", "chalk": "^2.4.1", "daemonize-process": "^1.0.9", "fixturez": "^1.1.0", "htpasswd-js": "^1.0.2", "micro-cors": "^0.1.1", "minimisted": "^2.0.0", "ssh-keygen": "^0.4.2", "ssh2": "^0.6.1", "tree-kill": "^1.2.0"}, "bin": {"git-http-mock-server": "http-daemon.js", "git-ssh-mock-server": "ssh-daemon.js"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globby": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/globby/-/globby-7.1.1.tgz", "integrity": "sha512-yANWAN2DUcBtuus5Cpd+SKROzXHs2iVXFZt/Ykrfz6SAXqacLX25NZpltE+39ceMexYF4TtEadjuSTw8+3wX4g==", "dev": true, "license": "MIT", "dependencies": {"array-union": "^1.0.1", "dir-glob": "^2.0.0", "glob": "^7.1.2", "ignore": "^3.3.5", "pify": "^3.0.0", "slash": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "license": "ISC"}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/htpasswd-js": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/htpasswd-js/-/htpasswd-js-1.0.2.tgz", "integrity": "sha512-KON5L4YKYXk647tmVclKgmHHG5nApjy9K+WiRoScnoWhS63lMoTca1ommUW2XQ3FDW8TtNDIQA7J0WYXICbMAA==", "dev": true, "license": "Apache-2.0", "dependencies": {"apache-crypt": "^1.2.1", "apache-md5": "^1.1.2", "bcryptjs": "^2.4.3", "fs-extra": "^4.0.2", "xerror": "^1.1.2"}}, "node_modules/htpasswd-js/node_modules/fs-extra": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-4.0.3.tgz", "integrity": "sha512-q6rbdDd1o2mAnQreO7YADIxf/Whx4AHBiRf6d+/cVT8h44ss+lHgxf1FemcqDnQt9X3ct4McHr+JMGlYSsK7Cg==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "3.3.10", "resolved": "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz", "integrity": "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==", "dev": true, "license": "MIT"}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/make-error": {"version": "1.3.6", "dev": true, "license": "ISC"}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micro-cors": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/micro-cors/-/micro-cors-0.1.1.tgz", "integrity": "sha512-6WqIahA5sbQR1Gjexp1VuWGFDKbZZleJb/gy1khNGk18a6iN1FdTcr3Q8twaxkV5H94RjxIBjirYbWCehpMBFw==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minimisted": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/minimisted/-/minimisted-2.0.1.tgz", "integrity": "sha512-1oPjfuLQa2caorJUM8HV8lGgWCc0qqAO1MNv/k05G4qslmsndV/5WdNZrqCiyqiz3wohia2Ij2B7w2Dr7/IyrA==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.5"}}, "node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-to-regexp": {"version": "0.1.12", "license": "MIT"}, "node_modules/path-type": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz", "integrity": "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==", "dev": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/pify": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "integrity": "sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/qs": {"version": "6.13.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/semver": {"version": "5.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "integrity": "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true, "license": "ISC"}, "node_modules/slash": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha512-3TYDR7xWt4dIqV2JauJr+EJeW356RXijHeUlO+8djJ+uBXPn8/2dpzBc8yQhh583sVvc9CvFAeQVgijsH+PNNg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ssh-keygen": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/ssh-keygen/-/ssh-keygen-0.4.2.tgz", "integrity": "sha512-SlEWW3cCtz87jwtCTfxo+tR+SQd4jJXWaBI/D9JVd74b2/N9ZvrWcd9lMFwFv0iMYb4aVAeMderH4AK5ZyW+Nw==", "dev": true, "dependencies": {"underscore": "1.4.x"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/ssh2": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/ssh2/-/ssh2-0.6.2.tgz", "integrity": "sha512-DJ+dOhXEEsmNpcQTI0x69FS++JH6qqL/ltEHf01pI1SSLMAcmD+hL4jRwvHjPwynPsmSUbHJ/WIZYzROfqZWjA==", "dev": true, "dependencies": {"ssh2-streams": "~0.2.0"}, "engines": {"node": ">=4.5.0"}}, "node_modules/ssh2-streams": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/ssh2-streams/-/ssh2-streams-0.2.1.tgz", "integrity": "sha512-3zCOsmunh1JWgPshfhKmBCL3lUtHPoh+a/cyQ49Ft0Q0aF7xgN06b76L+oKtFi0fgO57FLjFztb1GlJcEZ4a3Q==", "dev": true, "dependencies": {"asn1": "~0.2.0", "semver": "^5.1.0", "streamsearch": "~0.1.2"}, "engines": {"node": ">=4.5.0"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stream": {"version": "0.0.2", "license": "MIT", "dependencies": {"emitter-component": "^1.1.1"}}, "node_modules/streamsearch": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/temp-dir": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/temp-dir/-/temp-dir-1.0.0.tgz", "integrity": "sha512-xZFXEGbG7SNC3itwBzI3RYjq/cEhBkx2hJuKGIUOcEULmkQExXiHat2z/qkISYsuR+IKumhEfKKbV5qXmhICFQ==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/tempy": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/tempy/-/tempy-0.2.1.tgz", "integrity": "sha512-LB83o9bfZGrntdqPuRdanIVCPReam9SOZKW0fOy5I9X3A854GGWi0tjCqoXEk84XIEYBc/x9Hq3EFop/H5wJaw==", "dev": true, "license": "MIT", "dependencies": {"temp-dir": "^1.0.0", "unique-string": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tree-kill": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz", "integrity": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/ts-node": {"version": "10.9.2", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/underscore": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.4.4.tgz", "integrity": "sha512-ZqGrAgaqqZM7LGRzNjLnw5elevWb5M8LEoDMadxIW3OWbcv72wMMgKdwOKpd5Fqxe8choLD8HN3iSj3TUh/giQ==", "dev": true}, "node_modules/undici-types": {"version": "6.19.8", "dev": true, "license": "MIT"}, "node_modules/unique-string": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unique-string/-/unique-string-1.0.0.tgz", "integrity": "sha512-ODgiYu03y5g76A1I9Gt0/chLCzQjvzDy7DsZGsLOE/1MrF6wriEskSncj1+/C58Xk/kPZDppSctDybCwOSaGAg==", "dev": true, "license": "MIT", "dependencies": {"crypto-random-string": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/universalify": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unix-crypt-td-js": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/unix-crypt-td-js/-/unix-crypt-td-js-1.1.4.tgz", "integrity": "sha512-8rMeVYWSIyccIJscb9NdCfZKSRBKYTeVnwmiRYT2ulE3qd1RaDQ0xQDP+rI3ccIWbhu/zuo5cgN8z73belNZgw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true, "license": "ISC"}, "node_modules/xerror": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/xerror/-/xerror-1.1.3.tgz", "integrity": "sha512-2l5hmDymDUIuKT53v/nYxofTMUDQuu5P/Y3qHOjQiih6QUHBCgWpbpL3I8BoE5TVfUVTMmUQ0jdUAimTGc9UIg==", "dev": true, "license": "Apache-2.0"}, "node_modules/yn": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}}}