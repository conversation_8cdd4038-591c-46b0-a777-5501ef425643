{"name": "dyad", "productName": "dyad", "version": "0.11.1", "description": "My Electron application description", "main": ".vite/build/main.js", "repository": {"type": "git", "url": "https://github.com/dyad-sh/dyad.git"}, "engines": {"node": ">=20"}, "scripts": {"clean": "rm -rf out && rm -rf scaffold/node_modules", "start": "electron-forge start", "dev:engine": "DYAD_ENGINE_URL=http://localhost:8080/v1 npm start", "staging:engine": "DYAD_ENGINE_URL=https://staging---dyad-llm-engine-kq7pivehnq-uc.a.run.app/v1 npm start", "staging:gateway": "DYAD_GATEWAY_URL=https://staging---litellm-gcp-cloud-run-kq7pivehnq-uc.a.run.app/v1 npm start", "package": "npm run clean && electron-forge package", "make": "npm run clean && electron-forge make", "publish": "npm run clean && electron-forge publish", "ts": "npm run ts:main && npm run ts:workers", "ts:main": "npx tsc -p tsconfig.app.json --noEmit", "ts:workers": "npx tsc -p workers/tsc/tsconfig.json --noEmit", "lint": "npx oxlint --fix", "lint:fix": "npx oxlint --fix --fix-suggestions --fix-dangerously", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "prettier:check": "npx prettier --check .", "prettier": "npx prettier --write .", "presubmit": "npm run prettier:check && npm run lint", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "extract-codebase": "ts-node scripts/extract-codebase.ts", "prepare": "husky install", "pre:e2e": "cross-env E2E_TEST_BUILD=true npm run package", "e2e": "playwright test"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.0", "@electron-forge/maker-deb": "^7.8.0", "@electron-forge/maker-rpm": "^7.8.0", "@electron-forge/maker-squirrel": "^7.8.0", "@electron-forge/maker-zip": "^7.8.0", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron-forge/publisher-github": "^7.8.0", "@electron/fuses": "^1.8.0", "@playwright/test": "^1.52.0", "@testing-library/react": "^16.3.0", "@types/better-sqlite3": "^7.6.13", "@types/glob": "^8.1.0", "@types/kill-port": "^2.0.3", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitest/ui": "^3.1.1", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.6", "electron": "^37.2.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "happy-dom": "^17.4.4", "husky": "^9.1.7", "lint-staged": "^15.5.2", "oxlint": "^0.16.9", "prettier": "3.5.3", "typescript": "^5.8.3", "vite": "^5.4.17", "vitest": "^3.1.1"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.8", "@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.7", "@ai-sdk/openai-compatible": "^0.2.13", "@biomejs/biome": "^1.9.4", "@dyad-sh/supabase-management-js": "v1.0.0", "@monaco-editor/react": "^4.7.0-rc.0", "@openrouter/ai-sdk-provider": "^0.4.5", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@rollup/plugin-commonjs": "^28.0.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.75.5", "@tanstack/react-router": "^1.114.34", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "ai": "^4.3.4", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "electron-log": "^5.3.3", "electron-playwright-helpers": "^1.7.1", "electron-squirrel-startup": "^1.0.1", "esbuild-register": "^3.6.0", "fix-path": "^4.0.0", "framer-motion": "^12.6.3", "geist": "^1.3.1", "glob": "^11.0.2", "isomorphic-git": "^1.30.1", "jotai": "^2.12.2", "kill-port": "^2.0.1", "lucide-react": "^0.487.0", "monaco-editor": "^0.52.2", "ollama-ai-provider": "^1.2.0", "openai": "^4.91.1", "posthog-js": "^1.236.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-shiki": "^0.5.2", "remark-gfm": "^4.0.1", "shell-env": "^4.0.1", "shiki": "^3.2.1", "sonner": "^2.0.3", "stacktrace-js": "^2.0.2", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.1.3", "tree-kill": "^1.2.2", "tw-animate-css": "^1.2.5", "update-electron-app": "^3.1.1", "uuid": "^11.1.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "lint-staged": {"**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "ox<PERSON>", "*.{js,css,md,ts,tsx,jsx,json}": "prettier --write"}}